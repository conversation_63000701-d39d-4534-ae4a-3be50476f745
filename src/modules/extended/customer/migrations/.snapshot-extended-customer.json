{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "value": {"name": "value", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "customer_tag", "schema": "public", "indexes": [{"keyName": "IDX_customer_tag_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_customer_tag_deleted_at\" ON \"customer_tag\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "customer_tag_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "total_orders": {"name": "total_orders", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "total_sales": {"name": "total_sales", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "non_expirable_loyalty_points": {"name": "non_expirable_loyalty_points", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "expirable_loyalty_points": {"name": "expirable_loyalty_points", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "last_order_created_at": {"name": "last_order_created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "referred_users_count": {"name": "referred_users_count", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "extended_customer", "schema": "public", "indexes": [{"keyName": "IDX_extended_customer_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_extended_customer_deleted_at\" ON \"extended_customer\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "extended_customer_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"extended_customer_id": {"name": "extended_customer_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "customer_tag_id": {"name": "customer_tag_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}}, "name": "customer_tag_extended_customer", "schema": "public", "indexes": [{"keyName": "customer_tag_extended_customer_pkey", "columnNames": ["extended_customer_id", "customer_tag_id"], "composite": true, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"customer_tag_extended_customer_extended_customer_id_foreign": {"constraintName": "customer_tag_extended_customer_extended_customer_id_foreign", "columnNames": ["extended_customer_id"], "localTableName": "public.customer_tag_extended_customer", "referencedColumnNames": ["id"], "referencedTableName": "public.extended_customer", "deleteRule": "cascade", "updateRule": "cascade"}, "customer_tag_extended_customer_customer_tag_id_foreign": {"constraintName": "customer_tag_extended_customer_customer_tag_id_foreign", "columnNames": ["customer_tag_id"], "localTableName": "public.customer_tag_extended_customer", "referencedColumnNames": ["id"], "referencedTableName": "public.customer_tag", "deleteRule": "cascade", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}