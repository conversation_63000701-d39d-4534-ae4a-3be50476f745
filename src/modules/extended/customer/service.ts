import { Inject<PERSON>anager, MedusaContext, MedusaService, Modules } from '@medusajs/framework/utils'
import ExtendedCustomer from './models/extended-customer'
import CustomerTag from './models/customer-tag'
import { Context, CustomerDTO, ICustomerModuleService } from '@medusajs/framework/types'

// type InjectedDependencies = {
//   [Modules.CUSTOMER]: any // Replace 'any' with the actual type if available
// }

class ExtendedCustomerService extends MedusaService({
  ExtendedCustomer,
  CustomerTag,
}) {

  // protected customerService: any

  // constructor({ [Modules.CUSTOMER]: customerService }: InjectedDependencies) {
  //   super(...arguments)
  //   this.customerService = customerService
  // }
 
  // @InjectManager()
  // async upsertCustomer(
  //   customer : CustomerDTO,
  // ){    
  //   if ( !customer.id ){
  //     const createdCustomer = await this.customerService.createCustomers(customer)
  //     return createdCustomer
  //   }

  //   const createdCustomer = this.customerService.retrieveCustomer(customer.id)
  //   let newCustomer;

  //   if ( !createdCustomer ){
  //     newCustomer = this.customerService.createCustomers(customer)
  //   } else {
  //     newCustomer = this.customerService.updateCustomers(customer.id,{
  //       ...customer
  //     })
  //   }

  //   return newCustomer;
  // }

}

export default ExtendedCustomerService
