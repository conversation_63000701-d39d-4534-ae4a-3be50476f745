import { Modules } from "@medusajs/framework/utils";
import { EXTENDED_CUSTOMER_MODULE } from "../../modules/extended/customer";
import { CustomerDTOWithModifiedAddress, servicePack } from "../customer-migration";

export const createItemInDB = async (
    customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any },
    services: servicePack,
    container: any
  ) => {
    const { customerService, extendedCustomerService, link, query } = services;
    let msg = 'SUCCESS';
    let createdCustomer;

    createdCustomer = await extendedCustomerService.upsertCustomer(customer)
      
    // const { data: existingCustomersByPhone } = await query.graph({
    //   entity: 'customer',
    //   fields: ['id', 'phone'],
    //   filters: {
    //     //@ts-ignore
    //     phone: customer.phone
    //   }
    // });
  
    // const existingPhoneCustomer = existingCustomersByPhone?.[0];
  
    // if (existingPhoneCustomer && existingPhoneCustomer.id !== customer.id) {
    //   throw new Error(`Customer with phone ${customer.phone} already exists in the database.`);
    // }
  
    // try {
    //     console.log('---> Creating customer :::', customer);
    //   createdCustomer = await customerService.createCustomers(customer);
    // } catch (error) {
    //   if (error instanceof Error) {
    //     if (error.message.startsWith('Customer with id') && error.message.endsWith('already exists.')) {
    //       const { id, ...customerData } = customer;
    //       createdCustomer = await customerService.updateCustomers(id, customerData);
  
    //       const { data: [existingExtendedCustomer] } = await query.graph({
    //         entity: 'extended_customer',
    //         fields: ['id'],
    //         filters: {
    //           //@ts-ignore
    //           customer: { id: createdCustomer.id }
    //         }
    //       });
  
    //       if (existingExtendedCustomer) {
    //         await extendedCustomerService.softDeleteExtendedCustomers(existingExtendedCustomer.id);
    //       }
  
    //       await link.delete({
    //         [Modules.CUSTOMER]: {
    //           customer_id: createdCustomer.id,
    //         },
    //       });
  
    //       msg = `Customer with id ${customer.id} already exists. Updated existing customer.`;
    //     } else if (error.message.startsWith('Customer with email') && error.message.endsWith('already exists.')) {
    //       const customerWithoutEmail = { ...customer, email: undefined };
    //       createdCustomer = await customerService.createCustomers(customerWithoutEmail);
    //       msg = `Customer with email ${customer.email} already exists. Created new customer without email.`;
    //     } else {
    //       throw error;
    //     }
    //   } else {
    //     throw error;
    //   }
    // }
  
    const extendedCustomer = await extendedCustomerService.createExtendedCustomers({
      ...customer.additional_data
    });
  
    await link.create({
      [Modules.CUSTOMER]: {
        customer_id: createdCustomer.id,
      },
      [EXTENDED_CUSTOMER_MODULE]: {
        extended_customer_id: extendedCustomer.id,
      },
    });
  
    return msg;
  };
