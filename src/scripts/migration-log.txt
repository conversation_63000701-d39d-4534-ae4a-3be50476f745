Starting migration. Total rows: 167
Row 1: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 67.983,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.307Z,
  updated_at: 2025-06-01T17:11:51.307Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8B3AGEFDKD6H20JTJC'
}
Row 2: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 63.984,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.308Z,
  updated_at: 2025-06-01T17:11:51.308Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8CNWK9VJXPSNF89FV4'
}
Row 3: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.309Z,
  updated_at: 2025-06-01T17:11:51.309Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8D8E2YHWDA433VC6NZ'
}
Row 4: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.310Z,
  updated_at: 2025-06-01T17:11:51.310Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8E7M34Y3CNPTVXSEWD'
}
Row 5: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.313Z,
  updated_at: 2025-06-01T17:11:51.313Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8HVM8RPSMGPRWRT1PN'
}
Row 6: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.313Z,
  updated_at: 2025-06-01T17:11:51.313Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8HACD4ZMS67Z08Q3AF'
}
Row 7: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.314Z,
  updated_at: 2025-06-01T17:11:51.314Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8J9AQGWGF6EEPB0WKB'
}
Row 10: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.319Z,
  updated_at: 2025-06-01T17:11:51.319Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8Q3YKV7ZND9R617N1E'
}
Row 8: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.321Z,
  updated_at: 2025-06-01T17:11:51.321Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8SJCA0TXNBTMSMZ7VE'
}
Row 9: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.322Z,
  updated_at: 2025-06-01T17:11:51.322Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8SDDF3BBD6F6FDBN6Q'
}
Row 14: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.333Z,
  updated_at: 2025-06-01T17:11:51.333Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW95CRMB3FR5202TQG9J'
}
Row 12: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.334Z,
  updated_at: 2025-06-01T17:11:51.334Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9669QDF0DYKADRAFEQ'
}
Row 20: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.335Z,
  updated_at: 2025-06-01T17:11:51.335Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW97JZYEMMH6RBWVEWQK'
}
Row 16: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.336Z,
  updated_at: 2025-06-01T17:11:51.336Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW98JX0NKY0FHWD23C1K'
}
Row 19: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.337Z,
  updated_at: 2025-06-01T17:11:51.337Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW99GEBKQ2A22C6CHD3V'
}
Row 18: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.338Z,
  updated_at: 2025-06-01T17:11:51.338Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9APSG7C6J1MSRST63M'
}
Row 13: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.338Z,
  updated_at: 2025-06-01T17:11:51.338Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9AGYNGGQ0ET9DH5H2Q'
}
Row 11: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.338Z,
  updated_at: 2025-06-01T17:11:51.338Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9A2CHK0DMNYV5J09HQ'
}
Row 17: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.339Z,
  updated_at: 2025-06-01T17:11:51.339Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9B6T5QX6YPNP5F59RF'
}
Row 15: ERROR - Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.340Z,
  updated_at: 2025-06-01T17:11:51.340Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9CPTMPSBE9J9GJE4NC'
}
Migration complete.
