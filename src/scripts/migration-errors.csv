Customer ID,Error
18532392,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.313Z,
  updated_at: 2025-06-01T17:11:51.313Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8HACD4ZMS67Z08Q3AF'
}"
18532395,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.314Z,
  updated_at: 2025-06-01T17:11:51.314Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8J9AQGWGF6EEPB0WKB'
}"
18532399,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.319Z,
  updated_at: 2025-06-01T17:11:51.319Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8Q3YKV7ZND9R617N1E'
}"
18532396,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.321Z,
  updated_at: 2025-06-01T17:11:51.321Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8SJCA0TXNBTMSMZ7VE'
}"
18532398,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.322Z,
  updated_at: 2025-06-01T17:11:51.322Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW8SDDF3BBD6F6FDBN6Q'
}"
18532404,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.333Z,
  updated_at: 2025-06-01T17:11:51.333Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW95CRMB3FR5202TQG9J'
}"
18532402,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.334Z,
  updated_at: 2025-06-01T17:11:51.334Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9669QDF0DYKADRAFEQ'
}"
18532421,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.335Z,
  updated_at: 2025-06-01T17:11:51.335Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW97JZYEMMH6RBWVEWQK'
}"
18532408,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.336Z,
  updated_at: 2025-06-01T17:11:51.336Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW98JX0NKY0FHWD23C1K'
}"
18532420,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.337Z,
  updated_at: 2025-06-01T17:11:51.337Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW99GEBKQ2A22C6CHD3V'
}"
18532415,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.338Z,
  updated_at: 2025-06-01T17:11:51.338Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9APSG7C6J1MSRST63M'
}"
18532403,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.338Z,
  updated_at: 2025-06-01T17:11:51.338Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9AGYNGGQ0ET9DH5H2Q'
}"
18532401,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.338Z,
  updated_at: 2025-06-01T17:11:51.338Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9A2CHK0DMNYV5J09HQ'
}"
18532410,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.339Z,
  updated_at: 2025-06-01T17:11:51.339Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9B6T5QX6YPNP5F59RF'
}"
18532406,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:11:51.340Z,
  updated_at: 2025-06-01T17:11:51.340Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP7MW9CPTMPSBE9J9GJE4NC'
}"
