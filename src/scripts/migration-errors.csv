Customer ID,Error
18532385,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 67.983,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.153Z,
  updated_at: 2025-06-01T17:23:06.153Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F996SB7K807DK4CH4RB'
}"
18532386,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 63.984,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.157Z,
  updated_at: 2025-06-01T17:23:06.157Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F9D7YJ6BA0M9ST9FENM'
}"
18532388,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.167Z,
  updated_at: 2025-06-01T17:23:06.167Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F9Q660Z4DRZ046KM51X'
}"
18532391,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.172Z,
  updated_at: 2025-06-01T17:23:06.172Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F9WAJ47ZAVZH628BAMT'
}"
18532390,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.172Z,
  updated_at: 2025-06-01T17:23:06.172Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F9WY7CX3EDHZZ1MPAAQ'
}"
18532392,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.173Z,
  updated_at: 2025-06-01T17:23:06.173Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F9X1MWFVRJJ0PKX7K06'
}"
18532395,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.174Z,
  updated_at: 2025-06-01T17:23:06.174Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F9Y3XNDBAPDJHRR7VE6'
}"
18532398,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.176Z,
  updated_at: 2025-06-01T17:23:06.176Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FA0KFHFQN8RS4DEWGXV'
}"
18532396,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.175Z,
  updated_at: 2025-06-01T17:23:06.175Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89F9ZXYV3HDQ5NB0JMBGC'
}"
18532399,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.179Z,
  updated_at: 2025-06-01T17:23:06.179Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FA3RP92MY4MXC4M5DWQ'
}"
18532406,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.185Z,
  updated_at: 2025-06-01T17:23:06.185Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FA9TJERGMAJHTEP5Q21'
}"
18532408,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.185Z,
  updated_at: 2025-06-01T17:23:06.185Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FA92A7KFZ29QFAEBVEC'
}"
18532403,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.185Z,
  updated_at: 2025-06-01T17:23:06.185Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FA9RX7WYBGPBFE97CD4'
}"
18532401,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.186Z,
  updated_at: 2025-06-01T17:23:06.186Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FAA3S7PDMVRDZSD6XFF'
}"
18532404,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.186Z,
  updated_at: 2025-06-01T17:23:06.186Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FAABDY5AM63ACFFEGF9'
}"
18532421,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.187Z,
  updated_at: 2025-06-01T17:23:06.187Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FABVAC7QKZCYA5JV3WS'
}"
18532410,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.188Z,
  updated_at: 2025-06-01T17:23:06.188Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FACGZWWHASJ72BX5ZF7'
}"
18532415,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.188Z,
  updated_at: 2025-06-01T17:23:06.188Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FAC01NWH7FQ1TTHW4TR'
}"
18532402,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.187Z,
  updated_at: 2025-06-01T17:23:06.187Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FAA2VCSKABG7TQK9950'
}"
18532420,"Value for ExtendedCustomer.type is required, 'undefined' found
entity: ExtendedCustomer {
  total_orders: 0,
  total_sales: 0,
  non_expirable_loyalty_points: 0,
  expirable_loyalty_points: 0,
  notes: null,
  last_order_created_at: null,
  referred_users_count: 0,
  created_at: 2025-06-01T17:23:06.189Z,
  updated_at: 2025-06-01T17:23:06.189Z,
  deleted_at: null,
  customer_tags: Collection<CustomerTag> { initialized: true, dirty: false },
  id: '01JWP89FAD4FKDZAJY2E6YXSG7'
}"
