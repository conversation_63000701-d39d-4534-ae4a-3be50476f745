import { CustomerDTO } from "@medusajs/framework/types";
import { Modules } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";

export const upsertCustomerStep = createStep(
  'upsert-customer',
  async (customer: CustomerDTO, { container }) => {
    const customerService = container.resolve(Modules.CUSTOMER);
    const query = container.resolve('query');
    const logger = container.resolve('logger');

    logger.info(`Upserting customer with data: ${JSON.stringify({
      id: customer.id,
      email: customer.email,
      phone: customer.phone
    })}`);

    let existingCustomer: any = null;
    let upsertedCustomer: CustomerDTO | null = null;

    try {
      // First, try to find existing customer by ID if provided
      if (customer.id) {
        try {
          existingCustomer = await customerService.retrieveCustomer(customer.id);
          logger.info(`Found existing customer by ID: ${customer.id}`);
        } catch (error) {
          // Customer with ID doesn't exist, continue with other checks
          logger.info(`Customer with ID ${customer.id} not found, checking other identifiers`);
        }
      }

      // If not found by ID, try to find by email
      if (!existingCustomer && customer.email) {
        try {
          const { data: customersByEmail } = await query.graph({
            entity: 'customer',
            fields: ['id', 'email', 'phone', 'first_name', 'last_name'],
            //@ts-ignore
            filters: { email: customer.email as string }
          });

          if (customersByEmail && customersByEmail.length > 0) {
            existingCustomer = customersByEmail[0];
            logger.info(`Found existing customer by email: ${customer.email}`);
          }
        } catch (error: any) {
          logger.warn(`Error querying customer by email: ${error.message}`);
        }
      }

      // If not found by email, try to find by phone
      if (!existingCustomer && customer.phone) {
        try {
          const { data: customersByPhone } = await query.graph({
            entity: 'customer',
            fields: ['id', 'email', 'phone', 'first_name', 'last_name'],
            //@ts-ignore
            filters: { phone: customer.phone as string }
          });

          if (customersByPhone && customersByPhone.length > 0) {
            existingCustomer = customersByPhone[0];
            logger.info(`Found existing customer by phone: ${customer.phone}`);
          }
        } catch (error: any) {
          logger.warn(`Error querying customer by phone: ${error.message}`);
        }
      }

      // Prepare customer data for upsert
      const customerData = {
        ...customer,
        // Remove id if we're creating a new customer
        ...(existingCustomer ? { id: existingCustomer.id } : { id: undefined })
      };

      if (existingCustomer) {
        // Update existing customer
        logger.info(`Updating existing customer: ${existingCustomer.id}`);
        upsertedCustomer = await customerService.updateCustomers(existingCustomer.id, customerData);
      } else {
        // Create new customer
        logger.info(`Creating new customer`);
        upsertedCustomer = await customerService.createCustomers(customerData);
      }

      if (!upsertedCustomer) {
        throw new Error('Failed to upsert customer');
      }

      logger.info(`Successfully upserted customer: ${upsertedCustomer.id}`);

      return new StepResponse(upsertedCustomer, {
        customerId: upsertedCustomer.id,
        wasUpdate: !!existingCustomer,
        previousData: existingCustomer
      });

    } catch (error) {
      logger.error(`Error upserting customer:`, error);
      throw error;
    }
  },
  async (compensationData, { container }) => {
    if (!compensationData) return;

    const customerService = container.resolve(Modules.CUSTOMER);
    const logger = container.resolve('logger');

    try {
      if (compensationData.wasUpdate && compensationData.previousData) {
        // Revert to previous data
        logger.info(`Reverting customer update: ${compensationData.customerId}`);
        await customerService.updateCustomers(compensationData.customerId, compensationData.previousData);
      } else if (!compensationData.wasUpdate) {
        // Delete the created customer
        logger.info(`Deleting created customer: ${compensationData.customerId}`);
        await customerService.deleteCustomers([compensationData.customerId]);
      }
    } catch (error) {
      logger.error(`Error in customer upsert compensation:`, error);
    }
  }
);
