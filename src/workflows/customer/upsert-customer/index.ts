import { CustomerDTO } from "@medusajs/framework/types";
import { Modules } from "@medusajs/framework/utils";
import {
    createWorkflow,
    when,
    WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import {
    createRemoteLinkStep,
    useQueryGraphStep,
} from "@medusajs/medusa/core-flows";
import { checkCustomerTagsExistenceStep } from "../steps/check-customer-tags-existance";
import { EXTENDED_CUSTOMER_MODULE } from "../../../modules/extended/customer";
import { upsertExtendedCustomerStep } from "./steps/upsert-extended-customer";
import { upsertCustomerStep } from "./steps/upsert-customer";

export type UpsertCustomerWorkflowInput = {
    customer: CustomerDTO;
    additional_data?: { customer_tag_ids?: string[] };
};

export const upsertCustomer = createWorkflow(
    'upsert-customer',
    (input: UpsertCustomerWorkflowInput) => {
        // Validate customer tags if provided
        checkCustomerTagsExistenceStep({
            tag_ids: input.additional_data?.customer_tag_ids,
        });

        // Upsert the main customer record
        const customer = upsertCustomerStep(input.customer);

        // Query for existing extended customer data
        // @ts-ignore
        const { data: customers } = useQueryGraphStep({
            entity: 'customer',
            fields: ['extended_customer.*'],
            filters: { id: (customer as any).id },
        });

        // Handle extended customer data if additional_data is provided
        const extendedCustomer = when(
            'handle-extended-customer',
            { input, customers, customer },
            (data) => !!data.input.additional_data?.customer_tag_ids?.length
        ).then(() => {
            return upsertExtendedCustomerStep({
                customer_id: (customer as any).id,
                existing_extended_customer: customers[0]?.extended_customer,
                customer_tag_ids: input.additional_data?.customer_tag_ids,
            });
        });

        // Create remote link if extended customer was created and no link exists
        when(
            'create-remote-link',
            { customers, extendedCustomer },
            (data) =>
                !!data.extendedCustomer &&
                !data.customers[0]?.extended_customer
        ).then(() => {
            createRemoteLinkStep([
                {
                    [Modules.CUSTOMER]: { customer_id: (customer as any).id },
                    [EXTENDED_CUSTOMER_MODULE]: {
                        extended_customer_id: (extendedCustomer as any).id,
                    },
                },
            ]);
        });

        return new WorkflowResponse({
            customer,
            extended_customer: extendedCustomer,
        });
    }
);